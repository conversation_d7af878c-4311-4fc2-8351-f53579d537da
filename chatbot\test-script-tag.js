// Direct Script Tag API Test
// This script tests script tag creation using the provided access token

const ACCESS_TOKEN = '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';
const SHOP_DOMAIN = 'kamabaaecom.myshopify.com';
const API_VERSION = '2025-07';

async function testScriptTagCreation() {
  console.log('🚀 Testing Script Tag Creation...');
  console.log(`Shop: ${SHOP_DOMAIN}`);
  console.log(`Access Token: ${ACCESS_TOKEN.substring(0, 10)}...`);
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  const mutation = `
    mutation ScriptTagCreate($input: ScriptTagInput!) {
      scriptTagCreate(input: $input) {
        scriptTag {
          id
          cache
          createdAt
          displayScope
          src
          updatedAt
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  const variables = {
    input: {
      src: "https://js.example.org/test-script.js",
      displayScope: "ONLINE_STORE",
      cache: true
    }
  };
  
  try {
    console.log('📤 Sending GraphQL request...');
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query: mutation,
        variables: variables
      })
    });
    
    console.log(`📊 Response Status: ${response.status}`);
    
    const responseData = await response.json();
    console.log('📝 Response Data:', JSON.stringify(responseData, null, 2));
    
    if (responseData.data?.scriptTagCreate?.scriptTag) {
      console.log('✅ Script tag created successfully!');
      console.log('📋 Script Tag Details:');
      console.log(`   ID: ${responseData.data.scriptTagCreate.scriptTag.id}`);
      console.log(`   Source: ${responseData.data.scriptTagCreate.scriptTag.src}`);
      console.log(`   Display Scope: ${responseData.data.scriptTagCreate.scriptTag.displayScope}`);
      console.log(`   Cache: ${responseData.data.scriptTagCreate.scriptTag.cache}`);
      return responseData.data.scriptTagCreate.scriptTag;
    } else if (responseData.data?.scriptTagCreate?.userErrors?.length > 0) {
      console.log('❌ Script tag creation failed with errors:');
      responseData.data.scriptTagCreate.userErrors.forEach(error => {
        console.log(`   - ${error.field}: ${error.message}`);
      });
      return null;
    } else {
      console.log('❌ Unexpected response format');
      return null;
    }
    
  } catch (error) {
    console.error('💥 Error during script tag creation:', error);
    return null;
  }
}

async function listExistingScriptTags() {
  console.log('\n🔍 Listing existing script tags...');
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  const query = `
    query {
      scriptTags(first: 10) {
        edges {
          node {
            id
            src
            displayScope
            cache
            createdAt
            updatedAt
          }
        }
      }
    }
  `;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({ query })
    });
    
    const responseData = await response.json();
    
    if (responseData.data?.scriptTags?.edges) {
      const scriptTags = responseData.data.scriptTags.edges;
      console.log(`📊 Found ${scriptTags.length} script tags:`);
      
      scriptTags.forEach((edge, index) => {
        const tag = edge.node;
        console.log(`   ${index + 1}. ID: ${tag.id}`);
        console.log(`      Source: ${tag.src}`);
        console.log(`      Display Scope: ${tag.displayScope}`);
        console.log(`      Cache: ${tag.cache}`);
        console.log(`      Created: ${tag.createdAt}`);
        console.log('');
      });
      
      return scriptTags.map(edge => edge.node);
    } else {
      console.log('❌ Failed to retrieve script tags');
      console.log('Response:', JSON.stringify(responseData, null, 2));
      return [];
    }
    
  } catch (error) {
    console.error('💥 Error listing script tags:', error);
    return [];
  }
}

async function deleteScriptTag(scriptTagId) {
  console.log(`🗑️ Deleting script tag: ${scriptTagId}`);
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  const mutation = `
    mutation ScriptTagDelete($id: ID!) {
      scriptTagDelete(id: $id) {
        deletedScriptTagId
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query: mutation,
        variables: { id: scriptTagId }
      })
    });
    
    const responseData = await response.json();
    
    if (responseData.data?.scriptTagDelete?.deletedScriptTagId) {
      console.log('✅ Script tag deleted successfully');
      return true;
    } else {
      console.log('❌ Failed to delete script tag');
      console.log('Response:', JSON.stringify(responseData, null, 2));
      return false;
    }
    
  } catch (error) {
    console.error('💥 Error deleting script tag:', error);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🎯 Script Tag API Test Starting...\n');
  
  // List existing script tags
  const existingTags = await listExistingScriptTags();
  
  // Create a new script tag
  const newTag = await testScriptTagCreation();
  
  if (newTag) {
    console.log('\n⏳ Waiting 5 seconds before cleanup...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Clean up - delete the test script tag
    await deleteScriptTag(newTag.id);
  }
  
  console.log('\n🏁 Test completed!');
}

// Export for use in Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testScriptTagCreation,
    listExistingScriptTags,
    deleteScriptTag,
    main
  };
}

// Run if called directly
if (typeof require !== 'undefined' && require.main === module) {
  main().catch(console.error);
}
