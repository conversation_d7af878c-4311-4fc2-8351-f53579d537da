

/**
 * Creates a script tag in the Shopify store using GraphQL
 * @param {Object} admin - Shopify admin object
 * @param {string} src - Script source URL
 * @param {string} displayScope - Where to display the script (ONLINE_STORE, ORDER_STATUS, ALL)
 * @returns {Promise<Object>} Created script tag
 */
export async function createScriptTag(admin, src, displayScope = "ONLINE_STORE") {
  const mutation = `
    mutation ScriptTagCreate($input: ScriptTagInput!) {
      scriptTagCreate(input: $input) {
        scriptTag {
          id
          cache
          createdAt
          displayScope
          src
          updatedAt
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    input: {
      src: src,
      displayScope: displayScope,
      cache: true
    }
  };

  const response = await admin.graphql(mutation, { variables });
  const responseJson = await response.json();

  if (responseJson.data?.scriptTagCreate?.userErrors?.length > 0) {
    throw new Error(`Script tag creation failed: ${responseJson.data.scriptTagCreate.userErrors.map(e => e.message).join(', ')}`);
  }

  return responseJson.data?.scriptTagCreate?.scriptTag;
}

/**
 * Gets all script tags for the store using GraphQL
 * @param {Object} admin - Shopify admin object
 * @returns {Promise<Array>} Array of script tags
 */
export async function getScriptTags(admin) {
  const query = `
    query {
      scriptTags(first: 250) {
        edges {
          node {
            id
            src
            displayScope
            cache
            createdAt
            updatedAt
          }
        }
      }
    }
  `;

  const response = await admin.graphql(query);
  const responseJson = await response.json();

  if (responseJson.data?.scriptTags?.edges) {
    return responseJson.data.scriptTags.edges.map(edge => ({
      id: edge.node.id.replace('gid://shopify/ScriptTag/', ''),
      src: edge.node.src,
      display_scope: edge.node.displayScope,
      cache: edge.node.cache,
      created_at: edge.node.createdAt,
      updated_at: edge.node.updatedAt
    }));
  }

  return [];
}

/**
 * Finds script tags by source URL
 * @param {Object} admin - Shopify admin object
 * @param {string} src - Script source URL to search for
 * @returns {Promise<Array>} Array of matching script tags
 */
export async function findScriptTagsBySrc(admin, src) {
  const scriptTags = await getScriptTags(admin);
  return scriptTags.filter(tag => tag.src === src);
}

/**
 * Deletes a script tag by ID using GraphQL
 * @param {Object} admin - Shopify admin object
 * @param {string|number} scriptTagId - Script tag ID to delete
 * @returns {Promise<void>}
 */
export async function deleteScriptTag(admin, scriptTagId) {
  const mutation = `
    mutation ScriptTagDelete($id: ID!) {
      scriptTagDelete(id: $id) {
        deletedScriptTagId
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    id: `gid://shopify/ScriptTag/${scriptTagId}`
  };

  const response = await admin.graphql(mutation, { variables });
  const responseJson = await response.json();

  if (responseJson.data?.scriptTagDelete?.userErrors?.length > 0) {
    throw new Error(`Script tag deletion failed: ${responseJson.data.scriptTagDelete.userErrors.map(e => e.message).join(', ')}`);
  }
}

/**
 * Removes all script tags with a specific source URL
 * @param {Object} admin - Shopify admin object
 * @param {string} src - Script source URL to remove
 * @returns {Promise<number>} Number of script tags removed
 */
export async function removeScriptTagsBySrc(admin, src) {
  const scriptTags = await findScriptTagsBySrc(admin, src);

  for (const scriptTag of scriptTags) {
    await deleteScriptTag(admin, scriptTag.id);
  }

  return scriptTags.length;
}

/**
 * Ensures a script tag exists (creates if not found, updates if different)
 * @param {Object} admin - Shopify admin object
 * @param {string} src - Script source URL
 * @param {string} displayScope - Where to display the script
 * @returns {Promise<Object>} Script tag (created or existing)
 */
export async function ensureScriptTag(admin, src, displayScope = "ONLINE_STORE") {
  const existingTags = await findScriptTagsBySrc(admin, src);

  if (existingTags.length > 0) {
    // Return the first existing tag
    return existingTags[0];
  }

  // Create new script tag
  return await createScriptTag(admin, src, displayScope);
}

/**
 * Gets the chatbot script URL for the current app
 * @param {string} appUrl - The app's base URL
 * @param {string} shop - The shop domain (optional)
 * @returns {string} Full script URL
 */
export function getChatbotScriptUrl(appUrl, shop = null) {
  // Remove trailing slash if present
  const baseUrl = appUrl.replace(/\/$/, '');
  const scriptUrl = `${baseUrl}/chatbot-loader.js`;

  // Add shop parameter if provided
  if (shop) {
    return `${scriptUrl}?shop=${encodeURIComponent(shop)}`;
  }

  return scriptUrl;
}
