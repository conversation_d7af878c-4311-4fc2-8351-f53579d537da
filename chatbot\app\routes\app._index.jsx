import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>derD<PERSON> } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  InlineStack,
  Badge,
  Banner,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

// The ONLY script tag this app will inject
const HARDCODED_SCRIPT_SRC = "./chatbot.js";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);

  try {
    // Check if our hardcoded script tag exists using direct API
    const ACCESS_TOKEN = '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';
    const url = `https://${session.shop}/admin/api/2025-07/graphql.json`;
    
    const query = `
      query {
        scriptTags(first: 50) {
          edges {
            node {
              id
              src
              displayScope
              createdAt
            }
          }
        }
      }
    `;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({ query })
    });
    
    const data = await response.json();
    const scriptTags = data.data?.scriptTags?.edges?.map(edge => edge.node) || [];
    
    // Check if our hardcoded script is installed
    const isScriptInstalled = scriptTags.some(tag => tag.src === HARDCODED_SCRIPT_SRC);
    const installedScript = scriptTags.find(tag => tag.src === HARDCODED_SCRIPT_SRC);

    return Response.json({
      isScriptInstalled,
      installedScript,
      totalScriptTags: scriptTags.length,
      shop: session.shop,
      scriptSrc: HARDCODED_SCRIPT_SRC
    });

  } catch (error) {
    console.error("Error loading dashboard data:", error);
    return Response.json({
      isScriptInstalled: false,
      installedScript: null,
      totalScriptTags: 0,
      shop: session.shop,
      scriptSrc: HARDCODED_SCRIPT_SRC
    });
  }
};

export const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  const ACCESS_TOKEN = '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';

  try {
    switch (action) {
      case "install_script":
        console.log(`🚀 Installing hardcoded script tag: ${HARDCODED_SCRIPT_SRC}`);
        
        const createUrl = `https://${session.shop}/admin/api/2025-07/graphql.json`;
        const createMutation = `
          mutation ScriptTagCreate($input: ScriptTagInput!) {
            scriptTagCreate(input: $input) {
              scriptTag {
                id
                src
                displayScope
                createdAt
              }
              userErrors {
                field
                message
              }
            }
          }
        `;
        
        const createResponse = await fetch(createUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': ACCESS_TOKEN
          },
          body: JSON.stringify({
            query: createMutation,
            variables: {
              input: {
                src: HARDCODED_SCRIPT_SRC,
                displayScope: "ONLINE_STORE",
                cache: true
              }
            }
          })
        });
        
        const createData = await createResponse.json();
        
        if (createData.data?.scriptTagCreate?.scriptTag) {
          console.log(`✅ Script tag created successfully:`, createData.data.scriptTagCreate.scriptTag);
          return Response.json({
            success: true,
            message: "Hardcoded script tag installed successfully!",
            scriptTag: createData.data.scriptTagCreate.scriptTag
          });
        } else {
          const errors = createData.data?.scriptTagCreate?.userErrors || [];
          console.error(`❌ Script tag creation failed:`, errors);
          return Response.json({
            success: false,
            error: `Failed to install script tag: ${errors.map(e => e.message).join(', ')}`
          }, { status: 500 });
        }

      case "uninstall_script":
        console.log(`🗑️ Removing hardcoded script tag: ${HARDCODED_SCRIPT_SRC}`);
        
        // First, get all script tags to find our hardcoded one
        const listUrl = `https://${session.shop}/admin/api/2025-07/graphql.json`;
        const listQuery = `
          query {
            scriptTags(first: 50) {
              edges {
                node {
                  id
                  src
                }
              }
            }
          }
        `;
        
        const listResponse = await fetch(listUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': ACCESS_TOKEN
          },
          body: JSON.stringify({ query: listQuery })
        });
        
        const listData = await listResponse.json();
        const scriptTags = listData.data?.scriptTags?.edges?.map(edge => edge.node) || [];
        const targetScript = scriptTags.find(tag => tag.src === HARDCODED_SCRIPT_SRC);
        
        if (!targetScript) {
          return Response.json({
            success: false,
            error: "Hardcoded script tag not found"
          }, { status: 404 });
        }
        
        // Delete the script tag
        const deleteUrl = `https://${session.shop}/admin/api/2025-07/graphql.json`;
        const deleteMutation = `
          mutation ScriptTagDelete($id: ID!) {
            scriptTagDelete(id: $id) {
              deletedScriptTagId
              userErrors {
                field
                message
              }
            }
          }
        `;
        
        const deleteResponse = await fetch(deleteUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': ACCESS_TOKEN
          },
          body: JSON.stringify({
            query: deleteMutation,
            variables: { id: targetScript.id }
          })
        });
        
        const deleteData = await deleteResponse.json();
        
        if (deleteData.data?.scriptTagDelete?.deletedScriptTagId) {
          console.log(`✅ Script tag removed successfully`);
          return Response.json({
            success: true,
            message: "Hardcoded script tag removed successfully!"
          });
        } else {
          const errors = deleteData.data?.scriptTagDelete?.userErrors || [];
          console.error(`❌ Script tag removal failed:`, errors);
          return Response.json({
            success: false,
            error: `Failed to remove script tag: ${errors.map(e => e.message).join(', ')}`
          }, { status: 500 });
        }

      default:
        return Response.json({ error: "Unknown action" }, { status: 400 });
    }
    
  } catch (error) {
    console.error(`❌ Error in action:`, error);
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
};

export default function SimpleChatbotApp() {
  const data = useLoaderData();
  const fetcher = useFetcher();

  const isLoading = fetcher.state === "submitting";

  return (
    <Page>
      <TitleBar title="Simple Chatbot Script Injector" />
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">
                Hardcoded Script Injection
              </Text>
              
              <Text variant="bodyMd" color="subdued">
                This app injects only one hardcoded script tag: <code>&lt;script src="./chatbot.js" autostart="true"/&gt;</code>
              </Text>

              <Box padding="400" background="bg-surface-secondary" borderRadius="200">
                <BlockStack gap="200">
                  <Text variant="headingSm" as="h3">Script Details</Text>
                  <InlineStack align="space-between">
                    <Text>Script Source:</Text>
                    <Badge tone="info">{data.scriptSrc}</Badge>
                  </InlineStack>
                  <InlineStack align="space-between">
                    <Text>Status:</Text>
                    <Badge tone={data.isScriptInstalled ? "success" : "critical"}>
                      {data.isScriptInstalled ? "Installed" : "Not Installed"}
                    </Badge>
                  </InlineStack>
                  <InlineStack align="space-between">
                    <Text>Shop:</Text>
                    <Text variant="bodySm" color="subdued">{data.shop}</Text>
                  </InlineStack>
                </BlockStack>
              </Box>

              {data.isScriptInstalled && data.installedScript && (
                <Banner tone="success">
                  <Text>
                    ✅ Script tag is installed! Created on: {new Date(data.installedScript.createdAt).toLocaleString()}
                  </Text>
                </Banner>
              )}

              <InlineStack gap="300">
                {!data.isScriptInstalled ? (
                  <fetcher.Form method="post">
                    <input type="hidden" name="action" value="install_script" />
                    <Button 
                      variant="primary" 
                      loading={isLoading}
                      disabled={isLoading}
                    >
                      Install Hardcoded Script
                    </Button>
                  </fetcher.Form>
                ) : (
                  <fetcher.Form method="post">
                    <input type="hidden" name="action" value="uninstall_script" />
                    <Button 
                      variant="primary" 
                      tone="critical"
                      loading={isLoading}
                      disabled={isLoading}
                    >
                      Remove Script
                    </Button>
                  </fetcher.Form>
                )}
              </InlineStack>

              {fetcher.data?.success === true && (
                <Banner tone="success">
                  <Text>{fetcher.data.message}</Text>
                </Banner>
              )}

              {fetcher.data?.success === false && (
                <Banner tone="critical">
                  <Text>Error: {fetcher.data.error}</Text>
                </Banner>
              )}

              <Box padding="400" background="bg-surface-info" borderRadius="200">
                <BlockStack gap="200">
                  <Text variant="headingSm" as="h3">Verification Instructions</Text>
                  <Text variant="bodyMd">
                    To verify the script is working:
                  </Text>
                  <Text variant="bodySm" as="ol">
                    1. Visit your store: <strong>https://{data.shop}</strong><br/>
                    2. Right-click → "View Page Source" (Ctrl+U)<br/>
                    3. Search for: <strong>./chatbot.js</strong><br/>
                    4. You should see: <code>&lt;script src="./chatbot.js"&gt;</code> in the head section
                  </Text>
                </BlockStack>
              </Box>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
