import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  List,
  Link,
  InlineStack,
  <PERSON><PERSON>,
  <PERSON>,
  Spinner,
  <PERSON>Field,
  Form,
  FormLayout,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { getScriptTags, getChatbotScriptUrl, ensureScriptTag, removeScriptTagsBySrc } from "../utils/scriptTags.server";
import prisma from "../db.server";

export const loader = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);

  try {
    // Get current script tags
    const scriptTags = await getScriptTags(admin);
    const appUrl = process.env.SHOPIFY_APP_URL;
    const chatbotScriptUrl = appUrl ? getChatbotScriptUrl(appUrl, session.shop) : null;

    // Get chatbot configuration from database
    const chatbotConfig = await prisma.chatbotConfig.findUnique({
      where: { shop: session.shop }
    });

    // Find our chatbot script tag (either loader or configured script)
    const chatbotScriptTag = scriptTags.find(tag =>
      (chatbotScriptUrl && tag.src === chatbotScriptUrl) ||
      (chatbotConfig?.script_url && tag.src === chatbotConfig.script_url)
    );

    return Response.json({
      shop: session.shop,
      scriptTags: scriptTags,
      chatbotScriptTag: chatbotScriptTag,
      chatbotScriptUrl: chatbotScriptUrl,
      appUrl: appUrl,
      isInstalled: !!chatbotScriptTag,
      chatbotConfig: chatbotConfig,
      configuredScriptUrl: chatbotConfig?.script_url || ""
    });
  } catch (error) {
    console.error("Error loading dashboard data:", error);
    return Response.json({
      shop: session.shop,
      scriptTags: [],
      chatbotScriptTag: null,
      chatbotScriptUrl: null,
      appUrl: process.env.SHOPIFY_APP_URL,
      isInstalled: false,
      error: error.message,
      chatbotConfig: null,
      configuredScriptUrl: ""
    });
  }
};

export const action = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    const appUrl = process.env.SHOPIFY_APP_URL;
    if (!appUrl) {
      return Response.json({ error: "App URL not configured" }, { status: 500 });
    }

    const scriptUrl = getChatbotScriptUrl(appUrl, session.shop);

    switch (action) {
      case "install_script":
        // Get the configured script URL from database
        const chatbotConfig = await prisma.chatbotConfig.findUnique({
          where: { shop: session.shop }
        });

        // Use configured script URL if available, otherwise use loader script
        const targetScriptUrl = chatbotConfig?.script_url || scriptUrl;

        if (!chatbotConfig?.script_url) {
          return Response.json({
            error: "Please configure your chatbot embed script URL first before installing the script tag."
          }, { status: 400 });
        }

        console.log(`🚀 Installing script tag for shop: ${session.shop}, URL: ${targetScriptUrl}`);

        const scriptTag = await ensureScriptTag(admin, targetScriptUrl, "ONLINE_STORE");

        console.log(`✅ Script tag installed successfully:`, scriptTag);

        return Response.json({
          success: true,
          message: "Chatbot script tag installed successfully! Your configured script URL is now active on your storefront.",
          scriptTag,
          installedUrl: targetScriptUrl
        });

      case "uninstall_script":
        // Get the configured script URL from database
        const configForUninstall = await prisma.chatbotConfig.findUnique({
          where: { shop: session.shop }
        });

        // Remove both loader script and configured script if they exist
        let totalRemoved = 0;

        // Remove loader script
        const loaderRemoved = await removeScriptTagsBySrc(admin, scriptUrl);
        totalRemoved += loaderRemoved;

        // Remove configured script if it exists and is different from loader
        if (configForUninstall?.script_url && configForUninstall.script_url !== scriptUrl) {
          const configRemoved = await removeScriptTagsBySrc(admin, configForUninstall.script_url);
          totalRemoved += configRemoved;
        }

        return Response.json({
          success: true,
          message: `Removed ${totalRemoved} chatbot script tag(s)`,
          removedCount: totalRemoved
        });

      case "save_config":
        const scriptUrlConfig = formData.get("script_url");

        // Validate the script URL
        if (scriptUrlConfig && scriptUrlConfig.trim()) {
          try {
            new URL(scriptUrlConfig.trim());
          } catch {
            return Response.json({ error: "Please enter a valid URL" }, { status: 400 });
          }
        }

        // Save or update the configuration
        await prisma.chatbotConfig.upsert({
          where: { shop: session.shop },
          update: {
            script_url: scriptUrlConfig?.trim() || null,
            updated_at: new Date()
          },
          create: {
            shop: session.shop,
            script_url: scriptUrlConfig?.trim() || null
          }
        });

        return Response.json({
          success: true,
          message: "Script URL configuration saved successfully"
        });

      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in dashboard action:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
};

export default function ChatbotDashboard() {
  const data = useLoaderData();
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [scriptUrl, setScriptUrl] = useState(data.configuredScriptUrl || "");

  const isLoading = ["loading", "submitting"].includes(fetcher.state);

  useEffect(() => {
    if (fetcher.data?.success) {
      shopify.toast.show(fetcher.data.message);
      // Refresh the page data
      setIsRefreshing(true);
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else if (fetcher.data?.error) {
      shopify.toast.show(fetcher.data.error, { isError: true });
    }
  }, [fetcher.data, shopify]);

  const installScript = () => {
    fetcher.submit({ action: "install_script" }, { method: "POST" });
  };

  const uninstallScript = () => {
    fetcher.submit({ action: "uninstall_script" }, { method: "POST" });
  };

  const saveConfig = () => {
    fetcher.submit({ action: "save_config", script_url: scriptUrl }, { method: "POST" });
  };

  return (
    <Page>
      <TitleBar title="Chatbot Dashboard" />

      {data.error && (
        <Banner status="critical" title="Error loading dashboard">
          <p>{data.error}</p>
        </Banner>
      )}

      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    🤖 Chatbot Status for {data.shop}
                  </Text>
                  <InlineStack gap="200" align="start">
                    <Badge
                      status={data.isInstalled ? "success" : "attention"}
                      size="medium"
                    >
                      {data.isInstalled ? "Active" : "Not Installed"}
                    </Badge>
                    {isRefreshing && <Spinner size="small" />}
                  </InlineStack>
                </BlockStack>

                {!scriptUrl && (
                  <Banner tone="warning">
                    <Text variant="bodyMd">
                      ⚠️ No chatbot embed script URL configured. Please configure your script URL below before installing the chatbot.
                    </Text>
                  </Banner>
                )}

                {data.isInstalled ? (
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p" color="success">
                      ✅ Your chatbot is successfully installed and running on your storefront!
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-success"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border-success"
                    >
                      <BlockStack gap="200">
                        <Text as="h4" variant="headingSm">Script Tag Details:</Text>
                        <Text variant="bodySm">
                          <strong>Script URL:</strong> {data.chatbotScriptTag?.src}
                        </Text>
                        <Text variant="bodySm">
                          <strong>Display Scope:</strong> {data.chatbotScriptTag?.display_scope || 'online_store'}
                        </Text>
                        <Text variant="bodySm">
                          <strong>Created:</strong> {data.chatbotScriptTag?.created_at ? new Date(data.chatbotScriptTag.created_at).toLocaleString() : 'Unknown'}
                        </Text>
                      </BlockStack>
                    </Box>
                    <InlineStack gap="300">
                      <Button
                        variant="primary"
                        tone="critical"
                        loading={isLoading}
                        onClick={uninstallScript}
                      >
                        Uninstall Chatbot
                      </Button>
                      <Button
                        url={`https://${data.shop}`}
                        target="_blank"
                        variant="plain"
                      >
                        View Storefront
                      </Button>
                    </InlineStack>
                  </BlockStack>
                ) : (
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p">
                      Your chatbot is not currently installed on your storefront. Click the button below to install it.
                    </Text>
                    <Button
                      variant="primary"
                      loading={isLoading}
                      onClick={installScript}
                      disabled={!scriptUrl.trim()}
                    >
                      Install Chatbot
                    </Button>
                    {!scriptUrl.trim() && (
                      <Text variant="bodySm" color="subdued">
                        Configure your script URL first to enable installation
                      </Text>
                    )}
                  </BlockStack>
                )}
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    📊 Script Tags Overview
                  </Text>
                  <Text variant="bodyMd">
                    Total script tags: <strong>{data.scriptTags?.length || 0}</strong>
                  </Text>
                  {data.scriptTags?.length > 0 && (
                    <Box
                      padding="300"
                      background="bg-surface-secondary"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                    >
                      <BlockStack gap="100">
                        {data.scriptTags.slice(0, 5).map((tag, index) => (
                          <Text key={index} variant="bodySm">
                            • {tag.src?.split('/').pop() || 'Unknown script'}
                          </Text>
                        ))}
                        {data.scriptTags.length > 5 && (
                          <Text variant="bodySm" color="subdued">
                            ... and {data.scriptTags.length - 5} more
                          </Text>
                        )}
                      </BlockStack>
                    </Box>
                  )}
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    🔧 Script URL Configuration
                  </Text>
                  <Text variant="bodyMd" color="subdued">
                    Configure the chatbot embed script URL that will be loaded on your storefront.
                  </Text>
                  <FormLayout>
                    <TextField
                      label="Chatbot Embed Script URL"
                      value={scriptUrl}
                      onChange={setScriptUrl}
                      placeholder="https://your-domain.com/chatbot-embed.js"
                      helpText="Enter the full URL to your chatbot embed script. This will replace the hardcoded URL."
                      autoComplete="url"
                    />
                    <Button
                      variant="primary"
                      loading={isLoading}
                      onClick={saveConfig}
                      disabled={!scriptUrl.trim()}
                    >
                      Save Configuration
                    </Button>
                  </FormLayout>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    📋 System Configuration
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        App URL
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {data.appUrl ? new URL(data.appUrl).hostname : 'Not configured'}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Loader Script
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {data.chatbotScriptUrl ? '/chatbot-loader.js' : 'Not available'}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Configured Embed Script
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {scriptUrl || 'Not configured'}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Installed Script URL
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {data.chatbotScriptTag?.src || 'No script installed'}
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    📚 Features
                  </Text>
                  <List>
                    <List.Item>
                      Automatic customer identification
                    </List.Item>
                    <List.Item>
                      Real-time chat interface
                    </List.Item>
                    <List.Item>
                      Mobile-responsive design
                    </List.Item>
                    <List.Item>
                      Easy installation & removal
                    </List.Item>
                  </List>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}