// Test Script for Shopify Script Tag Verification
// This script will be loaded via script tag to verify functionality

(function() {
  'use strict';
  
  console.log('🚀 Test Script Loaded Successfully!');
  console.log('📅 Loaded at:', new Date().toISOString());
  console.log('🌐 User Agent:', navigator.userAgent);
  console.log('📍 Current URL:', window.location.href);
  
  // Create a visible indicator on the page
  function createTestIndicator() {
    // Check if indicator already exists
    if (document.getElementById('script-tag-test-indicator')) {
      console.log('⚠️ Test indicator already exists');
      return;
    }
    
    const indicator = document.createElement('div');
    indicator.id = 'script-tag-test-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #28a745;
      color: white;
      padding: 10px 15px;
      border-radius: 5px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      font-weight: bold;
      z-index: 9999;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      border: 2px solid #1e7e34;
    `;
    indicator.innerHTML = '✅ Script Tag Working!';
    
    // Add click handler to remove indicator
    indicator.addEventListener('click', function() {
      indicator.remove();
      console.log('🗑️ Test indicator removed');
    });
    
    document.body.appendChild(indicator);
    console.log('✅ Test indicator added to page');
    
    // Auto-remove after 10 seconds
    setTimeout(function() {
      if (document.getElementById('script-tag-test-indicator')) {
        indicator.remove();
        console.log('⏰ Test indicator auto-removed after 10 seconds');
      }
    }, 10000);
  }
  
  // Wait for DOM to be ready
  function waitForDOM() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', createTestIndicator);
      console.log('⏳ Waiting for DOM to load...');
    } else {
      createTestIndicator();
    }
  }
  
  // Send test data to console
  function logTestData() {
    const testData = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      domain: window.location.hostname,
      userAgent: navigator.userAgent,
      screenSize: `${screen.width}x${screen.height}`,
      windowSize: `${window.innerWidth}x${window.innerHeight}`,
      documentReady: document.readyState,
      hasJQuery: typeof jQuery !== 'undefined',
      hasShopify: typeof Shopify !== 'undefined'
    };
    
    console.log('📊 Test Data:', testData);
    
    // Try to store in sessionStorage for debugging
    try {
      sessionStorage.setItem('scriptTagTestData', JSON.stringify(testData));
      console.log('💾 Test data stored in sessionStorage');
    } catch (e) {
      console.log('⚠️ Could not store test data in sessionStorage:', e.message);
    }
  }
  
  // Main execution
  console.log('🎯 Starting script tag test...');
  logTestData();
  waitForDOM();
  
  // Dispatch custom event to notify other scripts
  if (typeof CustomEvent !== 'undefined') {
    const event = new CustomEvent('scriptTagTestLoaded', {
      detail: {
        timestamp: new Date().toISOString(),
        scriptUrl: document.currentScript ? document.currentScript.src : 'unknown'
      }
    });
    window.dispatchEvent(event);
    console.log('📡 Custom event dispatched: scriptTagTestLoaded');
  }
  
  console.log('✅ Script tag test completed successfully!');
  
})();

// Global function for manual testing
window.testScriptTagWorking = function() {
  console.log('🧪 Manual test function called');
  alert('✅ Script Tag is working! Check console for details.');
  return true;
};
