import { useLoaderData, useActionData, Form } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { ensureScriptTag, getChatbotScriptUrl, getScriptTags } from "../utils/scriptTags.server";

export async function loader({ request }) {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");

  if (!shop) {
    throw new Response("Shop parameter is required", { status: 400 });
  }

  try {
    // Try to authenticate and get session
    const { session } = await authenticate.admin(request);

    if (session) {
      // Check if script tag is already installed
      const appUrl = process.env.SHOPIFY_APP_URL;
      if (appUrl) {
        const scriptUrl = getChatbotScriptUrl(appUrl, session.shop);
        const scriptTags = await getScriptTags(session);
        const chatbotScriptTag = scriptTags.find(tag => tag.src === scriptUrl);

        return Response.json({
          shop,
          authenticated: true,
          scriptTagInstalled: !!chatbotScriptTag,
          scriptTag: chatbotScriptTag,
          scriptUrl
        });
      }
    }
  } catch (error) {
    console.log("Authentication failed in install route:", error.message);
  }

  return Response.json({
    shop,
    authenticated: false,
    scriptTagInstalled: false,
    scriptTag: null,
    scriptUrl: null
  });
}

export async function action({ request }) {
  try {
    const { session } = await authenticate.admin(request);
    const formData = await request.formData();
    const action = formData.get("action");

    if (action === "install_script") {
      const appUrl = process.env.SHOPIFY_APP_URL;
      if (!appUrl) {
        return Response.json({ error: "App URL not configured" }, { status: 500 });
      }

      const scriptUrl = getChatbotScriptUrl(appUrl);
      const scriptTag = await ensureScriptTag(session, scriptUrl, "online_store");

      return Response.json({
        success: true,
        message: "Chatbot script tag installed successfully",
        scriptTag
      });
    }

    return Response.json({ error: "Invalid action" }, { status: 400 });

  } catch (error) {
    console.error("Error in install action:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
}

export default function Install() {
  const data = useLoaderData();
  const actionData = useActionData();

  return (
    <div style={{ padding: "20px", maxWidth: "600px", margin: "0 auto" }}>
      <h1>🤖 Chatbot App Installation</h1>

      <div style={{ marginBottom: "20px", padding: "15px", backgroundColor: "#f0f8ff", border: "1px solid #0066cc", borderRadius: "5px" }}>
        <h2>✅ App installed on {data.shop}</h2>
        <p>Your chatbot app has been successfully installed!</p>
      </div>

      {data.authenticated ? (
        <div>
          <h3>Script Tag Status</h3>
          {data.scriptTagInstalled ? (
            <div style={{ padding: "10px", backgroundColor: "#d4edda", border: "1px solid #c3e6cb", borderRadius: "5px", marginBottom: "15px" }}>
              <p>✅ <strong>Chatbot script tag is installed and active</strong></p>
              <p>Script URL: <code>{data.scriptUrl}</code></p>
              <p>Script Tag ID: {data.scriptTag?.id}</p>
              <p>Display Scope: {data.scriptTag?.display_scope}</p>
            </div>
          ) : (
            <div>
              <div style={{ padding: "10px", backgroundColor: "#fff3cd", border: "1px solid #ffeaa7", borderRadius: "5px", marginBottom: "15px" }}>
                <p>⚠️ <strong>Chatbot script tag is not installed</strong></p>
                <p>The script tag needs to be installed for the chatbot to appear on your storefront.</p>
              </div>

              <Form method="post">
                <input type="hidden" name="action" value="install_script" />
                <button
                  type="submit"
                  style={{
                    padding: "10px 20px",
                    backgroundColor: "#0066cc",
                    color: "white",
                    border: "none",
                    borderRadius: "5px",
                    cursor: "pointer",
                    fontSize: "16px"
                  }}
                >
                  Install Chatbot Script Tag
                </button>
              </Form>
            </div>
          )}

          {actionData && (
            <div style={{
              marginTop: "15px",
              padding: "10px",
              backgroundColor: actionData.success ? "#d4edda" : "#f8d7da",
              border: `1px solid ${actionData.success ? "#c3e6cb" : "#f5c6cb"}`,
              borderRadius: "5px"
            }}>
              <p>{actionData.success ? "✅" : "❌"} {actionData.message || actionData.error}</p>
            </div>
          )}
        </div>
      ) : (
        <div style={{ padding: "10px", backgroundColor: "#f8d7da", border: "1px solid #f5c6cb", borderRadius: "5px" }}>
          <p>⚠️ <strong>Authentication required</strong></p>
          <p>Please complete the app installation process to manage script tags.</p>
        </div>
      )}

      <div style={{ marginTop: "30px", padding: "15px", backgroundColor: "#f8f9fa", border: "1px solid #dee2e6", borderRadius: "5px" }}>
        <h3>What happens next?</h3>
        <ul>
          <li>The chatbot script will be automatically loaded on your storefront</li>
          <li>Customer information (if logged in) will be passed to the chatbot</li>
          <li>The chatbot will appear according to your configuration</li>
          <li>Script tags are automatically managed during app installation/uninstallation</li>
        </ul>
      </div>

      <div style={{ marginTop: "20px", fontSize: "14px", color: "#666" }}>
        <p><strong>Note:</strong> If you're seeing this page, the app installation webhook should have automatically created the script tag. If not, you can manually install it using the button above.</p>
      </div>
    </div>
  );
}