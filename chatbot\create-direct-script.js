// Direct Script Tag Creation using your access token
// Run this with: node create-direct-script.js

const ACCESS_TOKEN = '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';
const SHOP_DOMAIN = 'kamabaaecom.myshopify.com';
const API_VERSION = '2025-07';

async function createDirectScriptTag() {
  console.log('🚀 Creating direct script tag...');
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  // Use a simple, accessible test script
  const scriptUrl = 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js';
  
  const mutation = `
    mutation ScriptTagCreate($input: ScriptTagInput!) {
      scriptTagCreate(input: $input) {
        scriptTag {
          id
          cache
          createdAt
          displayScope
          src
          updatedAt
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  const variables = {
    input: {
      src: scriptUrl,
      displayScope: "ONLINE_STORE",
      cache: true
    }
  };
  
  try {
    console.log(`📤 Sending request to: ${url}`);
    console.log(`📋 Script URL: ${scriptUrl}`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query: mutation,
        variables: variables
      })
    });
    
    console.log(`📊 Response Status: ${response.status}`);
    
    if (!response.ok) {
      console.error(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.error(`Error details: ${errorText}`);
      return;
    }
    
    const responseData = await response.json();
    console.log('📝 Response Data:', JSON.stringify(responseData, null, 2));
    
    if (responseData.data?.scriptTagCreate?.scriptTag) {
      const scriptTag = responseData.data.scriptTagCreate.scriptTag;
      console.log('✅ Script tag created successfully!');
      console.log(`   ID: ${scriptTag.id}`);
      console.log(`   Source: ${scriptTag.src}`);
      console.log(`   Display Scope: ${scriptTag.displayScope}`);
      console.log(`   Cache: ${scriptTag.cache}`);
      console.log(`   Created: ${scriptTag.createdAt}`);
      
      console.log('\n🌐 To verify:');
      console.log(`1. Visit your store: https://${SHOP_DOMAIN}`);
      console.log(`2. View page source (Ctrl+U)`);
      console.log(`3. Search for: ${scriptUrl}`);
      console.log(`4. You should see: <script src="${scriptUrl}"`);
      
      return scriptTag;
    } else if (responseData.data?.scriptTagCreate?.userErrors?.length > 0) {
      console.log('❌ Script tag creation failed with errors:');
      responseData.data.scriptTagCreate.userErrors.forEach(error => {
        console.log(`   - ${error.field}: ${error.message}`);
      });
    } else {
      console.log('❌ Unexpected response format');
      console.log('Response:', responseData);
    }
    
  } catch (error) {
    console.error('💥 Error during script tag creation:', error);
  }
}

async function listScriptTags() {
  console.log('\n🔍 Listing existing script tags...');
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  const query = `
    query {
      scriptTags(first: 10) {
        edges {
          node {
            id
            src
            displayScope
            cache
            createdAt
            updatedAt
          }
        }
      }
    }
  `;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({ query })
    });
    
    const responseData = await response.json();
    
    if (responseData.data?.scriptTags?.edges) {
      const scriptTags = responseData.data.scriptTags.edges;
      console.log(`📊 Found ${scriptTags.length} script tags:`);
      
      if (scriptTags.length === 0) {
        console.log('   No script tags found.');
      } else {
        scriptTags.forEach((edge, index) => {
          const tag = edge.node;
          console.log(`\n   ${index + 1}. ID: ${tag.id}`);
          console.log(`      Source: ${tag.src}`);
          console.log(`      Display Scope: ${tag.displayScope}`);
          console.log(`      Cache: ${tag.cache}`);
          console.log(`      Created: ${tag.createdAt}`);
        });
      }
      
      return scriptTags.map(edge => edge.node);
    } else {
      console.log('❌ Failed to retrieve script tags');
      console.log('Response:', JSON.stringify(responseData, null, 2));
      return [];
    }
    
  } catch (error) {
    console.error('💥 Error listing script tags:', error);
    return [];
  }
}

// Main execution
async function main() {
  console.log('🎯 Direct Script Tag Creation Test');
  console.log(`Shop: ${SHOP_DOMAIN}`);
  console.log(`Access Token: ${ACCESS_TOKEN.substring(0, 10)}...`);
  console.log('');
  
  // First, list existing script tags
  await listScriptTags();
  
  // Create a new script tag
  await createDirectScriptTag();
  
  console.log('\n🏁 Test completed!');
  console.log('\n📋 Next Steps:');
  console.log('1. Visit your Shopify store frontend');
  console.log('2. View page source (right-click → View Source)');
  console.log('3. Search for "jquery" or the script URL');
  console.log('4. You should see the script tag in the <head> section');
}

// Run the test
main().catch(console.error);
