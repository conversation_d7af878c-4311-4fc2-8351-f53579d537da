-- CreateTable
CREATE TABLE "Customer" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shopify_customer_id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "email" TEXT,
    "first_name" TEXT,
    "last_name" TEXT,
    "phone" TEXT,
    "accepts_marketing" BOOLEAN DEFAULT false,
    "created_at" DATETIME NOT NULL,
    "updated_at" DATETIME NOT NULL,
    "last_seen" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "ChatbotConfig" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "script_url" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateIndex
CREATE INDEX "Customer_shop_idx" ON "Customer"("shop");

-- CreateIndex
CREATE INDEX "Customer_last_seen_idx" ON "Customer"("last_seen");

-- CreateIndex
CREATE UNIQUE INDEX "Customer_shopify_customer_id_shop_key" ON "Customer"("shopify_customer_id", "shop");

-- CreateIndex
CREATE UNIQUE INDEX "ChatbotConfig_shop_key" ON "ChatbotConfig"("shop");

-- CreateIndex
CREATE INDEX "ChatbotConfig_shop_idx" ON "ChatbotConfig"("shop");
