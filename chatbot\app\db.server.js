import fs from 'fs';
import path from 'path';

// Simple JSON file-based storage for development
const CONFIG_FILE = path.join(process.cwd(), 'chatbot-config.json');

// Load existing configurations
let configData = {};
try {
  if (fs.existsSync(CONFIG_FILE)) {
    configData = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
  }
} catch (error) {
  console.warn('Could not load config file, starting with empty config');
}

// Save configurations to file
const saveConfig = () => {
  try {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(configData, null, 2));
  } catch (error) {
    console.error('Failed to save config:', error);
  }
};

// Create a simple database interface that works without Prisma
const createFallbackDB = () => ({
  chatbotConfig: {
    findUnique: async ({ where }) => {
      const config = configData[where.shop];
      return config || null;
    },
    upsert: async ({ where, update, create }) => {
      const shop = where.shop;
      const existingConfig = configData[shop];

      if (existingConfig) {
        const updatedConfig = { ...existingConfig, ...update, updated_at: new Date() };
        configData[shop] = updatedConfig;
        saveConfig();
        return updatedConfig;
      } else {
        const newConfig = {
          id: Date.now(),
          ...create,
          created_at: new Date(),
          updated_at: new Date()
        };
        configData[shop] = newConfig;
        saveConfig();
        return newConfig;
      }
    }
  }
});

// Always use fallback DB for now to avoid Prisma issues
const prisma = createFallbackDB();
console.log("✅ Using file-based configuration storage");

export default prisma;
