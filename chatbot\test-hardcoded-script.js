const ACCESS_TOKEN = '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';
const SHOP = 'kamabaaecom.myshopify.com';
const HARDCODED_SCRIPT_SRC = './chatbot.js';

async function installHardcodedScript() {
  console.log(`🚀 Installing hardcoded script tag: ${HARDCODED_SCRIPT_SRC}`);
  
  const url = `https://${SHOP}/admin/api/2025-07/graphql.json`;
  const mutation = `
    mutation ScriptTagCreate($input: ScriptTagInput!) {
      scriptTagCreate(input: $input) {
        scriptTag {
          id
          src
          displayScope
          createdAt
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query: mutation,
        variables: {
          input: {
            src: HARDCODED_SCRIPT_SRC,
            displayScope: "ONLINE_STORE",
            cache: true
          }
        }
      })
    });
    
    const data = await response.json();
    
    if (data.data?.scriptTagCreate?.scriptTag) {
      console.log(`✅ Hardcoded script tag created successfully:`, data.data.scriptTagCreate.scriptTag);
      console.log(`📍 Script source: ${data.data.scriptTagCreate.scriptTag.src}`);
      console.log(`🆔 Script ID: ${data.data.scriptTagCreate.scriptTag.id}`);
      return data.data.scriptTagCreate.scriptTag;
    } else {
      const errors = data.data?.scriptTagCreate?.userErrors || [];
      console.error(`❌ Script tag creation failed:`, errors);
      return null;
    }
  } catch (error) {
    console.error(`❌ Error installing script:`, error);
    return null;
  }
}

async function checkScriptStatus() {
  console.log(`🔍 Checking if hardcoded script exists: ${HARDCODED_SCRIPT_SRC}`);
  
  const url = `https://${SHOP}/admin/api/2025-07/graphql.json`;
  const query = `
    query {
      scriptTags(first: 50) {
        edges {
          node {
            id
            src
            displayScope
            createdAt
          }
        }
      }
    }
  `;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({ query })
    });
    
    const data = await response.json();
    const scriptTags = data.data?.scriptTags?.edges?.map(edge => edge.node) || [];
    
    console.log(`📊 Found ${scriptTags.length} total script tags:`);
    scriptTags.forEach((tag, index) => {
      console.log(`   ${index + 1}. ${tag.src} (ID: ${tag.id})`);
    });
    
    const hardcodedScript = scriptTags.find(tag => tag.src === HARDCODED_SCRIPT_SRC);
    
    if (hardcodedScript) {
      console.log(`✅ Hardcoded script IS installed:`);
      console.log(`   📍 Source: ${hardcodedScript.src}`);
      console.log(`   🆔 ID: ${hardcodedScript.id}`);
      console.log(`   📅 Created: ${hardcodedScript.createdAt}`);
      return true;
    } else {
      console.log(`❌ Hardcoded script NOT found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error checking script status:`, error);
    return false;
  }
}

async function main() {
  console.log(`🎯 Testing hardcoded script installation`);
  console.log(`📍 Script to install: <script src="${HARDCODED_SCRIPT_SRC}" autostart="true"/>`);
  console.log(`🏪 Shop: ${SHOP}`);
  console.log('');
  
  // Check current status
  const isInstalled = await checkScriptStatus();
  
  if (!isInstalled) {
    console.log('');
    console.log(`🚀 Installing hardcoded script...`);
    const result = await installHardcodedScript();
    
    if (result) {
      console.log('');
      console.log(`🎉 SUCCESS! Hardcoded script installed.`);
      console.log(`🌐 Visit your store to verify: https://${SHOP}`);
      console.log(`🔍 View page source and search for: ${HARDCODED_SCRIPT_SRC}`);
    } else {
      console.log('');
      console.log(`❌ FAILED to install hardcoded script.`);
    }
  } else {
    console.log('');
    console.log(`✅ Hardcoded script is already installed!`);
    console.log(`🌐 Visit your store to verify: https://${SHOP}`);
    console.log(`🔍 View page source and search for: ${HARDCODED_SCRIPT_SRC}`);
  }
}

main().catch(console.error);
