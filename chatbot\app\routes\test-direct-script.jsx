// Test route for direct script tag creation using access token
import { json } from "@remix-run/node";
import { createScriptTagDirect } from "../utils/scriptTags.server";

export async function action({ request }) {
  const formData = await request.formData();
  const action = formData.get("action");
  
  // Your provided access token
  const ACCESS_TOKEN = '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';
  const SHOP_DOMAIN = 'kamabaaecom.myshopify.com';
  
  console.log(`🎯 Direct script tag test - Action: ${action}`);
  
  try {
    switch (action) {
      case "create_direct_script":
        const scriptUrl = formData.get("script_url") || "https://js.example.org/test-direct.js";
        
        console.log(`🚀 Creating direct script tag for: ${scriptUrl}`);
        
        const scriptTag = await createScriptTagDirect(
          SHOP_DOMAIN,
          ACCESS_TOKEN,
          scriptUrl,
          "ONLINE_STORE"
        );
        
        return json({
          success: true,
          message: "Direct script tag created successfully!",
          scriptTag,
          accessTokenUsed: ACCESS_TOKEN.substring(0, 10) + "...",
          shop: SHOP_DOMAIN
        });
        
      case "list_script_tags":
        // List existing script tags using direct API
        const listUrl = `https://${SHOP_DOMAIN}/admin/api/2025-07/graphql.json`;
        
        const listQuery = `
          query {
            scriptTags(first: 10) {
              edges {
                node {
                  id
                  src
                  displayScope
                  cache
                  createdAt
                  updatedAt
                }
              }
            }
          }
        `;
        
        const listResponse = await fetch(listUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': ACCESS_TOKEN
          },
          body: JSON.stringify({ query: listQuery })
        });
        
        const listData = await listResponse.json();
        
        return json({
          success: true,
          message: "Script tags retrieved successfully!",
          scriptTags: listData.data?.scriptTags?.edges?.map(edge => edge.node) || [],
          count: listData.data?.scriptTags?.edges?.length || 0
        });
        
      default:
        return json({ error: "Unknown action" }, { status: 400 });
    }
    
  } catch (error) {
    console.error(`💥 Error in direct script tag test:`, error);
    return json({
      success: false,
      error: error.message,
      details: error.stack
    }, { status: 500 });
  }
}

export default function TestDirectScript() {
  return (
    <div style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}>
      <h1>🧪 Direct Script Tag API Test</h1>
      <p>This page tests direct script tag creation using your access token.</p>
      
      <div style={{ marginBottom: "20px", padding: "15px", backgroundColor: "#f5f5f5", borderRadius: "5px" }}>
        <h3>📋 Test Information</h3>
        <p><strong>Shop:</strong> kamabaaecom.myshopify.com</p>
        <p><strong>Access Token:</strong> shpat_8a947e66...*** (provided)</p>
        <p><strong>API Version:</strong> 2025-07</p>
      </div>
      
      <div style={{ display: "flex", gap: "20px", flexWrap: "wrap" }}>
        <form method="post" style={{ padding: "15px", border: "1px solid #ddd", borderRadius: "5px" }}>
          <h3>🚀 Create Direct Script Tag</h3>
          <input type="hidden" name="action" value="create_direct_script" />
          <div style={{ marginBottom: "10px" }}>
            <label>Script URL:</label><br />
            <input 
              type="url" 
              name="script_url" 
              defaultValue="https://js.example.org/test-direct.js"
              style={{ width: "300px", padding: "5px" }}
            />
          </div>
          <button 
            type="submit" 
            style={{ 
              backgroundColor: "#007cba", 
              color: "white", 
              padding: "10px 20px", 
              border: "none", 
              borderRadius: "3px",
              cursor: "pointer"
            }}
          >
            Create Script Tag
          </button>
        </form>
        
        <form method="post" style={{ padding: "15px", border: "1px solid #ddd", borderRadius: "5px" }}>
          <h3>📋 List Script Tags</h3>
          <input type="hidden" name="action" value="list_script_tags" />
          <p>View all existing script tags in your store.</p>
          <button 
            type="submit" 
            style={{ 
              backgroundColor: "#28a745", 
              color: "white", 
              padding: "10px 20px", 
              border: "none", 
              borderRadius: "3px",
              cursor: "pointer"
            }}
          >
            List Script Tags
          </button>
        </form>
      </div>
      
      <div style={{ marginTop: "30px", padding: "15px", backgroundColor: "#e7f3ff", borderRadius: "5px" }}>
        <h3>ℹ️ Instructions</h3>
        <ol>
          <li><strong>Create Script Tag:</strong> Enter a script URL and click "Create Script Tag" to test direct creation</li>
          <li><strong>List Script Tags:</strong> Click "List Script Tags" to see all existing script tags</li>
          <li><strong>Check Store:</strong> After creating, visit your store frontend and view source to see the script in the head</li>
        </ol>
      </div>
    </div>
  );
}
