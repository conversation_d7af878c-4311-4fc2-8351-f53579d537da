// Install the hardcoded script tag: <script src="./chatbot.js" autostart="true"/>
const ACCESS_TOKEN = '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';
const SHOP = 'kamabaaecom.myshopify.com';
const HARDCODED_SCRIPT_SRC = './chatbot.js';

console.log('🎯 Installing HARDCODED script tag');
console.log('📍 Script: <script src="./chatbot.js" autostart="true"/>');
console.log('🏪 Shop:', SHOP);
console.log('');

const url = `https://${SHOP}/admin/api/2025-07/graphql.json`;
const mutation = `
  mutation ScriptTagCreate($input: ScriptTagInput!) {
    scriptTagCreate(input: $input) {
      scriptTag {
        id
        src
        displayScope
        createdAt
      }
      userErrors {
        field
        message
      }
    }
  }
`;

fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Shopify-Access-Token': ACCESS_TOKEN
  },
  body: JSON.stringify({
    query: mutation,
    variables: {
      input: {
        src: HARDCODED_SCRIPT_SRC,
        displayScope: "ONLINE_STORE",
        cache: true
      }
    }
  })
})
.then(response => response.json())
.then(data => {
  console.log('📝 Response:', JSON.stringify(data, null, 2));
  
  if (data.data?.scriptTagCreate?.scriptTag) {
    console.log('');
    console.log('🎉 SUCCESS! Hardcoded script tag installed:');
    console.log('   📍 Source:', data.data.scriptTagCreate.scriptTag.src);
    console.log('   🆔 ID:', data.data.scriptTagCreate.scriptTag.id);
    console.log('   📅 Created:', data.data.scriptTagCreate.scriptTag.createdAt);
    console.log('');
    console.log('✅ The hardcoded script is now injected in your store head!');
    console.log('🌐 Visit your store: https://' + SHOP);
    console.log('🔍 View page source and search for: ' + HARDCODED_SCRIPT_SRC);
    console.log('');
    console.log('📋 You should see: <script src="./chatbot.js"> in the <head> section');
  } else {
    const errors = data.data?.scriptTagCreate?.userErrors || [];
    console.error('❌ Failed to create hardcoded script tag:', errors);
  }
})
.catch(error => {
  console.error('❌ Error:', error);
});
