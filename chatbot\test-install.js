// Test script installation with exact input value
const ACCESS_TOKEN = '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';
const SHOP_DOMAIN = 'kamabaaecom.myshopify.com';
const API_VERSION = '2025-07';

async function testScriptInstallation(scriptUrl) {
  console.log('🚀 Testing script installation...');
  console.log(`📋 Script URL (exact input): ${scriptUrl}`);
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  const mutation = `
    mutation ScriptTagCreate($input: ScriptTagInput!) {
      scriptTagCreate(input: $input) {
        scriptTag {
          id
          cache
          createdAt
          displayScope
          src
          updatedAt
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  const variables = {
    input: {
      src: scriptUrl,  // Use exact input value
      displayScope: "ONLINE_STORE",
      cache: true
    }
  };
  
  try {
    console.log(`📤 Creating script tag...`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query: mutation,
        variables: variables
      })
    });
    
    const responseData = await response.json();
    
    if (responseData.data?.scriptTagCreate?.scriptTag) {
      const scriptTag = responseData.data.scriptTagCreate.scriptTag;
      console.log('✅ Script tag created successfully!');
      console.log(`   ID: ${scriptTag.id}`);
      console.log(`   Source: ${scriptTag.src}`);
      console.log(`   Display Scope: ${scriptTag.displayScope}`);
      console.log(`   Created: ${scriptTag.createdAt}`);
      
      console.log('\n🌐 Verification Instructions:');
      console.log(`1. Visit your store: https://${SHOP_DOMAIN}`);
      console.log(`2. Right-click → "View Page Source" (or Ctrl+U)`);
      console.log(`3. Search for: ${scriptUrl}`);
      console.log(`4. You should see: <script src="${scriptUrl}"`);
      
      return scriptTag;
    } else if (responseData.data?.scriptTagCreate?.userErrors?.length > 0) {
      console.log('❌ Script tag creation failed with errors:');
      responseData.data.scriptTagCreate.userErrors.forEach(error => {
        console.log(`   - ${error.field}: ${error.message}`);
      });
      return null;
    } else {
      console.log('❌ Unexpected response format');
      console.log('Response:', responseData);
      return null;
    }
    
  } catch (error) {
    console.error('💥 Error during script tag creation:', error);
    return null;
  }
}

// Test with different script URLs
async function runTests() {
  console.log('🎯 Starting Script Installation Tests\n');
  
  const testUrls = [
    'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js',
    'https://js.example.org/test.js',
    'https://your-domain.com/chatbot.js'
  ];
  
  for (const url of testUrls) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`Testing: ${url}`);
    console.log(`${'='.repeat(60)}`);
    
    const result = await testScriptInstallation(url);
    
    if (result) {
      console.log(`✅ SUCCESS: Script tag created for ${url}`);
    } else {
      console.log(`❌ FAILED: Could not create script tag for ${url}`);
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🏁 All tests completed!');
  console.log('\n📋 Final Verification:');
  console.log(`Visit: https://${SHOP_DOMAIN}`);
  console.log('View page source and search for your script URLs');
}

// Run if called directly
if (typeof require !== 'undefined' && require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testScriptInstallation };
