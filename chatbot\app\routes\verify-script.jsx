// Route to verify script tags are working in the store
import { json } from "@remix-run/node";

export async function action({ request }) {
  const formData = await request.formData();
  const action = formData.get("action");
  
  const ACCESS_TOKEN = '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';
  const SHOP_DOMAIN = 'kamabaaecom.myshopify.com';
  
  try {
    switch (action) {
      case "list_script_tags":
        const url = `https://${SHOP_DOMAIN}/admin/api/2025-07/graphql.json`;
        
        const query = `
          query {
            scriptTags(first: 20) {
              edges {
                node {
                  id
                  src
                  displayScope
                  cache
                  createdAt
                  updatedAt
                }
              }
            }
          }
        `;
        
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': ACCESS_TOKEN
          },
          body: JSON.stringify({ query })
        });
        
        const data = await response.json();
        
        return json({
          success: true,
          scriptTags: data.data?.scriptTags?.edges?.map(edge => edge.node) || [],
          count: data.data?.scriptTags?.edges?.length || 0
        });
        
      case "delete_script_tag":
        const scriptTagId = formData.get("script_tag_id");
        
        if (!scriptTagId) {
          return json({ error: "Script tag ID is required" }, { status: 400 });
        }
        
        const deleteUrl = `https://${SHOP_DOMAIN}/admin/api/2025-07/graphql.json`;
        
        const deleteMutation = `
          mutation ScriptTagDelete($id: ID!) {
            scriptTagDelete(id: $id) {
              deletedScriptTagId
              userErrors {
                field
                message
              }
            }
          }
        `;
        
        const deleteResponse = await fetch(deleteUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': ACCESS_TOKEN
          },
          body: JSON.stringify({
            query: deleteMutation,
            variables: { id: scriptTagId }
          })
        });
        
        const deleteData = await deleteResponse.json();
        
        if (deleteData.data?.scriptTagDelete?.deletedScriptTagId) {
          return json({
            success: true,
            message: "Script tag deleted successfully",
            deletedId: deleteData.data.scriptTagDelete.deletedScriptTagId
          });
        } else {
          return json({
            success: false,
            error: "Failed to delete script tag",
            details: deleteData.data?.scriptTagDelete?.userErrors || []
          });
        }
        
      default:
        return json({ error: "Unknown action" }, { status: 400 });
    }
    
  } catch (error) {
    console.error("Error in verify-script:", error);
    return json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export default function VerifyScript() {
  return (
    <div style={{ padding: "20px", fontFamily: "Arial, sans-serif", maxWidth: "1200px" }}>
      <h1>🔍 Script Tag Verification</h1>
      <p>Verify and manage script tags in your Shopify store.</p>
      
      <div style={{ marginBottom: "20px", padding: "15px", backgroundColor: "#e7f3ff", borderRadius: "5px" }}>
        <h3>📋 Store Information</h3>
        <p><strong>Shop:</strong> kamabaaecom.myshopify.com</p>
        <p><strong>Store URL:</strong> <a href="https://kamabaaecom.myshopify.com" target="_blank">https://kamabaaecom.myshopify.com</a></p>
      </div>
      
      <div style={{ display: "flex", gap: "20px", marginBottom: "30px" }}>
        <form method="post" style={{ padding: "15px", border: "1px solid #ddd", borderRadius: "5px" }}>
          <h3>📋 List All Script Tags</h3>
          <input type="hidden" name="action" value="list_script_tags" />
          <p>View all script tags currently installed in your store.</p>
          <button 
            type="submit" 
            style={{ 
              backgroundColor: "#007cba", 
              color: "white", 
              padding: "10px 20px", 
              border: "none", 
              borderRadius: "3px",
              cursor: "pointer"
            }}
          >
            List Script Tags
          </button>
        </form>
      </div>
      
      <div style={{ padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "5px" }}>
        <h3>🔍 How to Verify Script Tags are Working</h3>
        <ol>
          <li><strong>Visit your store:</strong> <a href="https://kamabaaecom.myshopify.com" target="_blank">https://kamabaaecom.myshopify.com</a></li>
          <li><strong>View page source:</strong> Right-click → "View Page Source" (or Ctrl+U)</li>
          <li><strong>Search for scripts:</strong> Press Ctrl+F and search for your script URL</li>
          <li><strong>Check console:</strong> Press F12 → Console tab to see if scripts loaded</li>
          <li><strong>Test jQuery:</strong> If jQuery is installed, type <code>$.fn.jquery</code> in console</li>
        </ol>
      </div>
      
      <div style={{ marginTop: "20px", padding: "15px", backgroundColor: "#d4edda", borderRadius: "5px" }}>
        <h3>✅ Success Indicators</h3>
        <ul>
          <li><strong>In page source:</strong> You should see <code>&lt;script src="your-script-url"&gt;</code> in the &lt;head&gt; section</li>
          <li><strong>In console:</strong> No 404 errors for your script URLs</li>
          <li><strong>For jQuery:</strong> <code>$.fn.jquery</code> returns version number (e.g., "3.6.0")</li>
          <li><strong>Custom scripts:</strong> Any console.log messages from your scripts appear</li>
        </ul>
      </div>
    </div>
  );
}
