// Final working script to install hardcoded script tag
const https = require('https');

const ACCESS_TOKEN = '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';
const SHOP = 'kamabaaecom.myshopify.com';
const HARDCODED_SCRIPT_SRC = './chatbot.js';

console.log('🎯 INSTALLING HARDCODED SCRIPT TAG');
console.log('📍 Script: <script src="./chatbot.js" autostart="true"/>');
console.log('🏪 Shop:', SHOP);
console.log('');

// GraphQL mutation to create script tag
const mutation = `
mutation ScriptTagCreate($input: ScriptTagInput!) {
  scriptTagCreate(input: $input) {
    scriptTag {
      id
      src
      displayScope
      createdAt
    }
    userErrors {
      field
      message
    }
  }
}
`;

const requestData = JSON.stringify({
  query: mutation,
  variables: {
    input: {
      src: HARDCODED_SCRIPT_SRC,
      displayScope: "ONLINE_STORE",
      cache: true
    }
  }
});

const options = {
  hostname: SHOP,
  port: 443,
  path: '/admin/api/2025-07/graphql.json',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Shopify-Access-Token': ACCESS_TOKEN,
    'Content-Length': Buffer.byteLength(requestData)
  }
};

console.log('📤 Sending request to Shopify API...');
console.log('📋 Request data:', requestData);

const req = https.request(options, (res) => {
  console.log('📥 Response status:', res.statusCode, res.statusMessage);
  
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('📝 Full response:', JSON.stringify(response, null, 2));
      
      if (response.data?.scriptTagCreate?.scriptTag) {
        const scriptTag = response.data.scriptTagCreate.scriptTag;
        console.log('');
        console.log('🎉 SUCCESS! Hardcoded script tag installed:');
        console.log('   📍 Source:', scriptTag.src);
        console.log('   🆔 ID:', scriptTag.id);
        console.log('   📅 Created:', scriptTag.createdAt);
        console.log('   🎯 Display Scope:', scriptTag.displayScope);
        console.log('');
        console.log('✅ The hardcoded script is now injected in your store head!');
        console.log('🌐 Visit your store: https://' + SHOP);
        console.log('🔍 View page source and search for: ' + HARDCODED_SCRIPT_SRC);
        console.log('');
        console.log('📋 Expected in head: <script src="./chatbot.js"></script>');
      } else {
        const errors = response.data?.scriptTagCreate?.userErrors || [];
        console.error('');
        console.error('❌ FAILED! Script tag creation failed:');
        console.error('❌ Errors:', errors);
        console.error('❌ Full response:', response);
      }
    } catch (error) {
      console.error('❌ Error parsing response:', error);
      console.error('❌ Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request error:', error);
});

req.write(requestData);
req.end();
