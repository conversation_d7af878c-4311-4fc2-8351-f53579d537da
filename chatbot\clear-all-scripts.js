// Clear all script tags from the store
const ACCESS_TOKEN = '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';
const SHOP_DOMAIN = 'kamabaaecom.myshopify.com';
const API_VERSION = '2025-07';

async function listAllScriptTags() {
  console.log('🔍 Listing all script tags...');
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  const query = `
    query {
      scriptTags(first: 50) {
        edges {
          node {
            id
            src
            displayScope
            cache
            createdAt
          }
        }
      }
    }
  `;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({ query })
    });
    
    const data = await response.json();
    
    if (data.data?.scriptTags?.edges) {
      const scriptTags = data.data.scriptTags.edges.map(edge => edge.node);
      console.log(`📊 Found ${scriptTags.length} script tags:`);
      
      scriptTags.forEach((tag, index) => {
        console.log(`   ${index + 1}. ID: ${tag.id}`);
        console.log(`      Source: ${tag.src}`);
        console.log(`      Created: ${tag.createdAt}`);
        console.log('');
      });
      
      return scriptTags;
    } else {
      console.log('❌ Failed to retrieve script tags');
      return [];
    }
    
  } catch (error) {
    console.error('💥 Error listing script tags:', error);
    return [];
  }
}

async function deleteScriptTag(scriptTagId) {
  console.log(`🗑️ Deleting script tag: ${scriptTagId}`);
  
  const url = `https://${SHOP_DOMAIN}/admin/api/${API_VERSION}/graphql.json`;
  
  const mutation = `
    mutation ScriptTagDelete($id: ID!) {
      scriptTagDelete(id: $id) {
        deletedScriptTagId
        userErrors {
          field
          message
        }
      }
    }
  `;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({
        query: mutation,
        variables: { id: scriptTagId }
      })
    });
    
    const data = await response.json();
    
    if (data.data?.scriptTagDelete?.deletedScriptTagId) {
      console.log(`✅ Successfully deleted: ${scriptTagId}`);
      return true;
    } else {
      console.log(`❌ Failed to delete: ${scriptTagId}`);
      if (data.data?.scriptTagDelete?.userErrors) {
        data.data.scriptTagDelete.userErrors.forEach(error => {
          console.log(`   Error: ${error.message}`);
        });
      }
      return false;
    }
    
  } catch (error) {
    console.error(`💥 Error deleting script tag ${scriptTagId}:`, error);
    return false;
  }
}

async function clearAllScriptTags() {
  console.log('🧹 Starting to clear all script tags...');
  
  // Get all script tags
  const scriptTags = await listAllScriptTags();
  
  if (scriptTags.length === 0) {
    console.log('✅ No script tags to delete');
    return;
  }
  
  console.log(`🎯 Will delete ${scriptTags.length} script tags...`);
  
  let deletedCount = 0;
  let failedCount = 0;
  
  // Delete each script tag
  for (const tag of scriptTags) {
    const success = await deleteScriptTag(tag.id);
    if (success) {
      deletedCount++;
    } else {
      failedCount++;
    }
    
    // Add small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n🏁 Cleanup completed!');
  console.log(`✅ Successfully deleted: ${deletedCount} script tags`);
  console.log(`❌ Failed to delete: ${failedCount} script tags`);
  
  // Verify cleanup
  console.log('\n🔍 Verifying cleanup...');
  const remainingTags = await listAllScriptTags();
  
  if (remainingTags.length === 0) {
    console.log('🎉 All script tags successfully removed!');
  } else {
    console.log(`⚠️ ${remainingTags.length} script tags still remain`);
  }
}

// Run the cleanup
clearAllScriptTags().catch(console.error);
