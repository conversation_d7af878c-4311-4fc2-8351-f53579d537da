// Simple script to install the hardcoded script tag
const ACCESS_TOKEN = '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';
const SHOP = 'kamabaaecom.myshopify.com';
const HARDCODED_SCRIPT_SRC = './chatbot.js';

console.log('🚀 Installing hardcoded script tag...');
console.log('📍 Script source:', HARDCODED_SCRIPT_SRC);
console.log('🏪 Shop:', SHOP);

const url = `https://${SHOP}/admin/api/2025-07/graphql.json`;
const mutation = `
  mutation ScriptTagCreate($input: ScriptTagInput!) {
    scriptTagCreate(input: $input) {
      scriptTag {
        id
        src
        displayScope
        createdAt
      }
      userErrors {
        field
        message
      }
    }
  }
`;

fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Shopify-Access-Token': ACCESS_TOKEN
  },
  body: JSON.stringify({
    query: mutation,
    variables: {
      input: {
        src: HARDCODED_SCRIPT_SRC,
        displayScope: "ONLINE_STORE",
        cache: true
      }
    }
  })
})
.then(response => response.json())
.then(data => {
  console.log('📝 Full response:', JSON.stringify(data, null, 2));

  if (data.data?.scriptTagCreate?.scriptTag) {
    console.log('✅ SUCCESS! Hardcoded script tag created:');
    console.log('   📍 Source:', data.data.scriptTagCreate.scriptTag.src);
    console.log('   🆔 ID:', data.data.scriptTagCreate.scriptTag.id);
    console.log('   📅 Created:', data.data.scriptTagCreate.scriptTag.createdAt);
    console.log('');
    console.log('🌐 Visit your store to verify: https://' + SHOP);
    console.log('🔍 View page source and search for: ' + HARDCODED_SCRIPT_SRC);
  } else {
    const errors = data.data?.scriptTagCreate?.userErrors || [];
    console.error('❌ Failed to create script tag:', errors);
    console.error('❌ Full data:', data);
  }
})
.catch(error => {
  console.error('❌ Error:', error);
});
