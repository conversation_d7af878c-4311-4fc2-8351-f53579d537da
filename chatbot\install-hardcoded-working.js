// Working script to install the hardcoded script tag: <script src="./chatbot.js" autostart="true"/>
const ACCESS_TOKEN = '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';
const SHOP = 'kamabaaecom.myshopify.com';
const HARDCODED_SCRIPT_SRC = './chatbot.js';

console.log('🎯 Installing HARDCODED script tag');
console.log('📍 Script: <script src="./chatbot.js" autostart="true"/>');
console.log('🏪 Shop:', SHOP);
console.log('🔑 Access Token:', ACCESS_TOKEN.substring(0, 10) + '...');
console.log('');

async function installHardcodedScript() {
  try {
    const url = `https://${SHOP}/admin/api/2025-07/graphql.json`;
    const mutation = `
      mutation ScriptTagCreate($input: ScriptTagInput!) {
        scriptTagCreate(input: $input) {
          scriptTag {
            id
            src
            displayScope
            createdAt
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const requestBody = {
      query: mutation,
      variables: {
        input: {
          src: HARDCODED_SCRIPT_SRC,
          displayScope: "ONLINE_STORE",
          cache: true
        }
      }
    };

    console.log('📤 Sending request...');
    console.log('📋 Request body:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify(requestBody)
    });

    console.log('📥 Response status:', response.status, response.statusText);

    const data = await response.json();
    console.log('📝 Full response:', JSON.stringify(data, null, 2));

    if (data.data?.scriptTagCreate?.scriptTag) {
      console.log('');
      console.log('🎉 SUCCESS! Hardcoded script tag installed:');
      console.log('   📍 Source:', data.data.scriptTagCreate.scriptTag.src);
      console.log('   🆔 ID:', data.data.scriptTagCreate.scriptTag.id);
      console.log('   📅 Created:', data.data.scriptTagCreate.scriptTag.createdAt);
      console.log('');
      console.log('✅ The hardcoded script is now injected in your store head!');
      console.log('🌐 Visit your store: https://' + SHOP);
      console.log('🔍 View page source and search for: ' + HARDCODED_SCRIPT_SRC);
      console.log('');
      console.log('📋 You should see: <script src="./chatbot.js"> in the <head> section');
      return true;
    } else {
      const errors = data.data?.scriptTagCreate?.userErrors || [];
      console.error('❌ FAILED! Script tag creation failed:', errors);
      console.error('❌ Full response:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ ERROR:', error.message);
    console.error('❌ Stack:', error.stack);
    return false;
  }
}

async function checkScriptStatus() {
  try {
    console.log('🔍 Checking current script tags...');
    
    const url = `https://${SHOP}/admin/api/2025-07/graphql.json`;
    const query = `
      query {
        scriptTags(first: 50) {
          edges {
            node {
              id
              src
              displayScope
              createdAt
            }
          }
        }
      }
    `;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': ACCESS_TOKEN
      },
      body: JSON.stringify({ query })
    });

    const data = await response.json();
    const scriptTags = data.data?.scriptTags?.edges?.map(edge => edge.node) || [];

    console.log(`📊 Found ${scriptTags.length} total script tags:`);
    scriptTags.forEach((tag, index) => {
      console.log(`   ${index + 1}. ${tag.src} (ID: ${tag.id})`);
    });

    const hardcodedScript = scriptTags.find(tag => tag.src === HARDCODED_SCRIPT_SRC);
    
    if (hardcodedScript) {
      console.log('');
      console.log('✅ Hardcoded script IS already installed:');
      console.log('   📍 Source:', hardcodedScript.src);
      console.log('   🆔 ID:', hardcodedScript.id);
      console.log('   📅 Created:', hardcodedScript.createdAt);
      return true;
    } else {
      console.log('');
      console.log('❌ Hardcoded script NOT found');
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking script status:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting hardcoded script installation process...');
  console.log('');
  
  // Check current status
  const isInstalled = await checkScriptStatus();
  
  if (!isInstalled) {
    console.log('');
    console.log('🚀 Installing hardcoded script...');
    const success = await installHardcodedScript();
    
    if (success) {
      console.log('');
      console.log('🎉 INSTALLATION COMPLETE!');
    } else {
      console.log('');
      console.log('❌ INSTALLATION FAILED!');
    }
  } else {
    console.log('');
    console.log('✅ Script is already installed! No action needed.');
    console.log('🌐 Visit your store to verify: https://' + SHOP);
    console.log('🔍 View page source and search for: ' + HARDCODED_SCRIPT_SRC);
  }
}

// Run the script
main().catch(console.error);
