// Safe script loader route that handles external scripts with proper DOM checks
import prisma from "../db.server";

export async function loader({ request }) {
  const url = new URL(request.url);
  const targetScript = url.searchParams.get('src');
  const shop = url.searchParams.get('shop');
  
  if (!targetScript) {
    return new Response('Missing src parameter', { status: 400 });
  }

  // Generate a safe wrapper script
  const script = `
(function() {
  'use strict';
  
  // Ensure we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    console.warn('Script loader requires browser environment');
    return;
  }
  
  console.log('🔄 Safe script loader initializing for: ${targetScript}');
  
  // Wait for DOM to be ready
  function loadTargetScript() {
    if (!document.head || !document.body) {
      console.log('DOM not ready, retrying in 100ms...');
      setTimeout(loadTargetScript, 100);
      return;
    }
    
    console.log('📦 Loading target script: ${targetScript}');
    
    // Create script element
    var script = document.createElement('script');
    script.src = '${targetScript}';
    script.async = true;
    script.defer = true;
    
    // Add crossorigin for external scripts
    if (!script.src.includes(window.location.hostname)) {
      script.crossOrigin = 'anonymous';
    }
    
    // Add data attributes for debugging
    script.setAttribute('data-loaded-by', 'safe-script-loader');
    script.setAttribute('data-load-time', new Date().toISOString());
    script.setAttribute('data-shop', '${shop || 'unknown'}');
    
    // Set up event handlers
    script.onload = function() {
      console.log('✅ Target script loaded successfully: ${targetScript}');
      
      // Dispatch custom event to notify other scripts
      if (window.CustomEvent) {
        var event = new CustomEvent('safeScriptLoaded', {
          detail: { src: '${targetScript}', success: true }
        });
        window.dispatchEvent(event);
      }
    };
    
    script.onerror = function(error) {
      console.error('❌ Failed to load target script: ${targetScript}', error);
      
      // Dispatch error event
      if (window.CustomEvent) {
        var event = new CustomEvent('safeScriptError', {
          detail: { src: '${targetScript}', error: error }
        });
        window.dispatchEvent(event);
      }
    };
    
    // Append to head
    try {
      document.head.appendChild(script);
      console.log('📝 Script tag added to document head');
    } catch (error) {
      console.error('❌ Failed to append script to head:', error);
    }
  }
  
  // Start loading when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadTargetScript);
  } else {
    // DOM is already ready
    loadTargetScript();
  }
  
  console.log('🚀 Safe script loader initialized');
})();
  `.trim();

  return new Response(script, {
    headers: {
      "Content-Type": "application/javascript",
      "Cache-Control": "public, max-age=300", // Cache for 5 minutes
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
